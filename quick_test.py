#!/usr/bin/env python3
"""
快速弹幕测试 - 使用预设的测试数据
"""

import asyncio
import httpx
import json

async def quick_test():
    print("🚀 快速弹幕API测试")
    print("=" * 40)
    
    client = httpx.AsyncClient(
        timeout=10.0,
        headers={
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
    )
    
    try:
        # 测试1: 腾讯视频弹幕索引API
        print("\n1️⃣ 测试腾讯视频弹幕索引API")
        test_vid = "mzc00200mp8vo9e"  # 示例视频ID
        url = f"https://dm.video.qq.com/barrage/base/{test_vid}"
        print(f"📡 请求: {url}")
        
        try:
            response = await client.get(url)
            if response.status_code == 200:
                data = response.json()
                segment_count = len(data.get("segment_index", {}))
                print(f"✅ 成功! 找到 {segment_count} 个弹幕分段")
                if segment_count > 0:
                    print("📋 分段信息:")
                    for key, segment in list(data["segment_index"].items())[:3]:
                        print(f"  - 时间点 {key}: {segment.get('segment_name', 'N/A')}")
            else:
                print(f"❌ 失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        # 测试2: 爱奇艺弹幕API
        print("\n2️⃣ 测试爱奇艺弹幕API")
        test_tvid = "1234567890123456"  # 示例tvid
        s1, s2 = test_tvid[-4:-2], test_tvid[-2:]
        url = f"http://cmts.iqiyi.com/bullet/{s1}/{s2}/{test_tvid}_300_1.z"
        print(f"📡 请求: {url}")
        
        try:
            response = await client.get(url)
            if response.status_code == 200:
                print(f"✅ 成功! 获取到 {len(response.content)} 字节的压缩数据")
            elif response.status_code == 404:
                print("ℹ️  该测试ID无弹幕数据 (404) - 这是正常的")
            else:
                print(f"❌ 失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        # 测试3: Bilibili API
        print("\n3️⃣ 测试Bilibili视频信息API")
        test_bv = "BV1xx411c7mD"  # 示例BV号
        url = f"https://api.bilibili.com/x/web-interface/view?bvid={test_bv}"
        print(f"📡 请求: {url}")
        
        try:
            response = await client.get(url)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    video_data = data.get("data", {})
                    title = video_data.get("title", "未知标题")
                    aid = video_data.get("aid")
                    cid = video_data.get("cid")
                    print(f"✅ 成功! 视频: {title}")
                    print(f"📝 aid: {aid}, cid: {cid}")
                else:
                    print(f"❌ API错误: {data.get('message', '未知错误')}")
            else:
                print(f"❌ 失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        # 测试4: 优酷API连通性
        print("\n4️⃣ 测试优酷API连通性")
        url = "https://acs.youku.com/h5/mtop.com.youku.aplatform.weakget/1.0/?jsv=2.5.1&appKey=24679788"
        print(f"📡 请求: {url}")
        
        try:
            response = await client.get(url)
            if response.status_code == 200:
                print("✅ 优酷API连接正常")
            else:
                print(f"❌ 失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        # 测试5: 芒果TV API
        print("\n5️⃣ 测试芒果TV API")
        test_vid = "12345678"
        url = f"https://bullet-ws.hitv.com/bullet/role/all/{test_vid}"
        print(f"📡 请求: {url}")
        
        try:
            response = await client.get(url)
            if response.status_code == 200:
                print("✅ 芒果TV API连接正常")
                try:
                    data = response.json()
                    print(f"📋 响应结构: {type(data)} - {list(data.keys()) if isinstance(data, dict) else 'non-dict'}")
                except:
                    print("📋 响应不是JSON格式")
            else:
                print(f"❌ 失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    finally:
        await client.aclose()
    
    print("\n" + "=" * 40)
    print("🎯 快速测试完成!")
    print("\n💡 说明:")
    print("- ✅ 表示API连接正常")
    print("- ❌ 表示连接失败或错误")
    print("- ℹ️  表示正常的无数据响应")
    print("- 真实测试需要有效的视频ID")

if __name__ == "__main__":
    asyncio.run(quick_test())
