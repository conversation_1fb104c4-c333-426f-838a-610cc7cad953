#!/usr/bin/env python3
"""
真实弹幕测试脚本
测试从真实视频URL获取弹幕
"""

import asyncio
import re
import json
import zlib
import xml.etree.ElementTree as ET
from typing import Optional
import httpx

class DanmakuTester:
    def __init__(self):
        self.client = httpx.AsyncClient(
            timeout=20.0,
            follow_redirects=True,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
        )
    
    async def close(self):
        await self.client.aclose()
    
    async def test_tencent_url(self, url: str):
        """测试腾讯视频URL"""
        print(f"🎬 测试腾讯视频: {url}")
        
        # 从URL提取vid
        match = re.search(r'/([a-zA-Z0-9]+)\.html', url)
        if not match:
            print("❌ 无法从URL中提取vid")
            return
        
        vid = match.group(1)
        print(f"📝 提取到vid: {vid}")
        
        try:
            # 获取弹幕索引
            index_url = f"https://dm.video.qq.com/barrage/base/{vid}"
            response = await self.client.get(index_url)
            response.raise_for_status()
            
            index_data = response.json()
            segment_index = index_data.get("segment_index", {})
            
            if not segment_index:
                print("❌ 该视频没有弹幕")
                return
            
            print(f"✅ 找到 {len(segment_index)} 个弹幕分段")
            
            # 获取前3个分段的弹幕
            total_comments = 0
            for i, (key, segment) in enumerate(list(segment_index.items())[:3]):
                segment_name = segment.get("segment_name")
                if not segment_name:
                    continue
                
                segment_url = f"https://dm.video.qq.com/barrage/segment/{vid}/{segment_name}"
                response = await self.client.get(segment_url)
                response.raise_for_status()
                
                comment_data = response.json()
                barrage_list = comment_data.get("barrage_list", [])
                total_comments += len(barrage_list)
                
                print(f"  分段 {i+1}: {len(barrage_list)} 条弹幕")
                
                # 显示前3条弹幕
                for j, comment in enumerate(barrage_list[:3]):
                    content = comment.get("content", "")
                    time_offset = comment.get("time_offset", "0")
                    time_sec = int(time_offset) / 1000.0
                    print(f"    [{time_sec:.1f}s] {content}")
            
            print(f"🎯 总计获取 {total_comments} 条弹幕")
            
        except Exception as e:
            print(f"❌ 获取失败: {e}")
    
    async def test_iqiyi_tvid(self, tvid: str):
        """测试爱奇艺tvid"""
        print(f"🎭 测试爱奇艺tvid: {tvid}")
        
        if len(tvid) < 4:
            print("❌ tvid长度不足")
            return
        
        try:
            s1 = tvid[-4:-2]
            s2 = tvid[-2:]
            
            total_comments = 0
            # 测试前5个分段
            for mat in range(1, 6):
                url = f"http://cmts.iqiyi.com/bullet/{s1}/{s2}/{tvid}_300_{mat}.z"
                
                try:
                    response = await self.client.get(url)
                    if response.status_code == 404:
                        print(f"  分段 {mat}: 无数据 (404)")
                        break
                    
                    response.raise_for_status()
                    
                    # 解压数据
                    decompressed_data = zlib.decompress(response.content)
                    
                    # 解析XML
                    root = ET.fromstring(decompressed_data)
                    comments = root.findall('.//bulletInfo')
                    
                    total_comments += len(comments)
                    print(f"  分段 {mat}: {len(comments)} 条弹幕")
                    
                    # 显示前3条弹幕
                    for i, comment in enumerate(comments[:3]):
                        content_node = comment.find('content')
                        show_time_node = comment.find('showTime')
                        
                        if content_node is not None and show_time_node is not None:
                            content = content_node.text or ""
                            show_time = show_time_node.text or "0"
                            time_sec = int(show_time) / 1000.0
                            print(f"    [{time_sec:.1f}s] {content}")
                    
                    await asyncio.sleep(0.2)  # 礼貌等待
                    
                except Exception as e:
                    print(f"  分段 {mat}: 获取失败 - {e}")
                    break
            
            print(f"🎯 总计获取 {total_comments} 条弹幕")
            
        except Exception as e:
            print(f"❌ 获取失败: {e}")
    
    async def test_bilibili_bv(self, bvid: str):
        """测试Bilibili BV号"""
        print(f"📺 测试Bilibili: {bvid}")
        
        try:
            # 获取视频信息
            url = f"https://api.bilibili.com/x/web-interface/view?bvid={bvid}"
            response = await self.client.get(url)
            response.raise_for_status()
            
            data = response.json()
            if data.get("code") != 0:
                print(f"❌ API错误: {data.get('message', '未知错误')}")
                return
            
            video_data = data.get("data", {})
            aid = video_data.get("aid")
            cid = video_data.get("cid")
            title = video_data.get("title", "")
            
            if not aid or not cid:
                print("❌ 无法获取aid或cid")
                return
            
            print(f"📝 视频: {title}")
            print(f"📝 aid: {aid}, cid: {cid}")
            
            # 获取弹幕 (测试第一个分段)
            dm_url = f"https://api.bilibili.com/x/v2/dm/web/seg.so?type=1&oid={cid}&pid={aid}&segment_index=1"
            response = await self.client.get(dm_url)
            
            if response.status_code == 200 and response.content:
                print(f"✅ 获取到弹幕数据 ({len(response.content)} bytes)")
                print("ℹ️  B站弹幕使用protobuf格式，需要专门的解析库")
            else:
                print(f"❌ 弹幕获取失败 (状态码: {response.status_code})")
            
        except Exception as e:
            print(f"❌ 获取失败: {e}")

async def main():
    print("🚀 真实弹幕获取测试")
    print("=" * 50)
    
    tester = DanmakuTester()
    
    try:
        # 测试示例 - 你可以替换为真实的URL或ID
        
        print("\n1️⃣ 腾讯视频测试")
        print("请输入腾讯视频URL (或按回车跳过):")
        tencent_url = input().strip()
        if tencent_url:
            await tester.test_tencent_url(tencent_url)
        else:
            print("⏭️ 跳过腾讯视频测试")
        
        print("\n2️⃣ 爱奇艺测试")
        print("请输入爱奇艺tvid (16位数字，或按回车跳过):")
        iqiyi_tvid = input().strip()
        if iqiyi_tvid:
            await tester.test_iqiyi_tvid(iqiyi_tvid)
        else:
            print("⏭️ 跳过爱奇艺测试")
        
        print("\n3️⃣ Bilibili测试")
        print("请输入Bilibili BV号 (如BV1xx411c7mD，或按回车跳过):")
        bilibili_bv = input().strip()
        if bilibili_bv:
            await tester.test_bilibili_bv(bilibili_bv)
        else:
            print("⏭️ 跳过Bilibili测试")
        
    finally:
        await tester.close()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    print("\n💡 使用说明:")
    print("- 腾讯视频: 复制视频页面URL")
    print("- 爱奇艺: 需要16位数字的tvid")
    print("- Bilibili: 输入BV号")

if __name__ == "__main__":
    asyncio.run(main())
