#!/usr/bin/env python3
"""
弹幕爬虫测试脚本 - 无需数据库
直接测试各个平台的弹幕获取功能
"""

import asyncio
import sys
import json
from typing import Optional, Dict, Any
import httpx

# 模拟配置管理器
class MockConfigManager:
    def __init__(self):
        self.config = {
            "proxy_url": "",
            "proxy_enabled": "false",
            "search_ttl_seconds": "300",
            "episodes_ttl_seconds": "1800"
        }
    
    async def get(self, key: str, default: str = "") -> str:
        return self.config.get(key, default)

# 模拟数据库池
class MockPool:
    pass

# 简化的爬虫基类
class SimpleScraper:
    def __init__(self, provider_name: str):
        self.provider_name = provider_name
        self.pool = MockPool()
        self.config_manager = MockConfigManager()
        self.client: Optional[httpx.AsyncClient] = None
    
    async def _create_client(self, **kwargs) -> httpx.AsyncClient:
        client_kwargs = {"timeout": 20.0, "follow_redirects": True, **kwargs}
        return httpx.AsyncClient(**client_kwargs)
    
    async def close(self):
        if self.client:
            await self.client.aclose()

# 腾讯视频测试
async def test_tencent_danmaku():
    print("🎬 测试腾讯视频弹幕获取...")
    
    # 创建简化的腾讯爬虫
    scraper = SimpleScraper("tencent")
    scraper.client = await scraper._create_client()
    
    # 测试视频ID (这是一个示例ID，你可以替换为其他的)
    test_vid = "mzc00200mp8vo9e"  # 示例视频ID
    
    try:
        # 1. 获取弹幕分段索引
        index_url = f"https://dm.video.qq.com/barrage/base/{test_vid}"
        print(f"📡 请求弹幕索引: {index_url}")
        
        response = await scraper.client.get(index_url)
        response.raise_for_status()
        index_data = response.json()
        
        segment_index = index_data.get("segment_index", {})
        if not segment_index:
            print(f"❌ 视频 {test_vid} 没有弹幕数据")
            return
        
        print(f"✅ 找到 {len(segment_index)} 个弹幕分段")
        
        # 2. 获取第一个分段的弹幕
        first_key = list(segment_index.keys())[0]
        segment = segment_index[first_key]
        segment_name = segment.get("segment_name")
        
        if segment_name:
            segment_url = f"https://dm.video.qq.com/barrage/segment/{test_vid}/{segment_name}"
            print(f"📡 请求弹幕分段: {segment_url}")
            
            response = await scraper.client.get(segment_url)
            response.raise_for_status()
            comment_data = response.json()
            
            barrage_list = comment_data.get("barrage_list", [])
            print(f"✅ 获取到 {len(barrage_list)} 条弹幕")
            
            # 显示前5条弹幕
            for i, comment in enumerate(barrage_list[:5]):
                content = comment.get("content", "")
                time_offset = comment.get("time_offset", "0")
                time_sec = int(time_offset) / 1000.0
                print(f"  {i+1}. [{time_sec:.1f}s] {content}")
        
    except Exception as e:
        print(f"❌ 腾讯视频测试失败: {e}")
    finally:
        await scraper.close()

# Bilibili测试
async def test_bilibili_danmaku():
    print("\n📺 测试Bilibili弹幕获取...")
    
    scraper = SimpleScraper("bilibili")
    scraper.client = await scraper._create_client()
    
    # 测试视频 (BV号转换为aid和cid)
    test_aid = "170001"  # 示例aid
    test_cid = "279786"  # 示例cid
    
    try:
        # 获取弹幕 (使用新的分段API)
        url = f"https://api.bilibili.com/x/v2/dm/web/seg.so?type=1&oid={test_cid}&pid={test_aid}&segment_index=1"
        print(f"📡 请求B站弹幕: {url}")
        
        response = await scraper.client.get(url)
        if response.status_code == 200 and response.content:
            print(f"✅ 获取到弹幕数据 ({len(response.content)} bytes)")
            print("ℹ️  B站弹幕使用protobuf格式，需要特殊解析")
        else:
            print(f"❌ 未获取到弹幕数据 (状态码: {response.status_code})")
            
    except Exception as e:
        print(f"❌ Bilibili测试失败: {e}")
    finally:
        await scraper.close()

# 爱奇艺测试
async def test_iqiyi_danmaku():
    print("\n🎭 测试爱奇艺弹幕获取...")
    
    scraper = SimpleScraper("iqiyi")
    scraper.client = await scraper._create_client()
    
    # 测试视频ID
    test_tvid = "1234567890123456"  # 示例tvid (16位数字)
    
    try:
        # 构造弹幕URL
        s1 = test_tvid[-4:-2]
        s2 = test_tvid[-2:]
        url = f"http://cmts.iqiyi.com/bullet/{s1}/{s2}/{test_tvid}_300_1.z"
        print(f"📡 请求爱奇艺弹幕: {url}")
        
        response = await scraper.client.get(url)
        if response.status_code == 200:
            print(f"✅ 获取到压缩弹幕数据 ({len(response.content)} bytes)")
            print("ℹ️  爱奇艺弹幕使用zlib压缩的XML格式")
        elif response.status_code == 404:
            print("ℹ️  该视频没有弹幕数据 (404)")
        else:
            print(f"❌ 请求失败 (状态码: {response.status_code})")
            
    except Exception as e:
        print(f"❌ 爱奇艺测试失败: {e}")
    finally:
        await scraper.close()

# 优酷测试
async def test_youku_danmaku():
    print("\n🎪 测试优酷弹幕获取...")
    
    scraper = SimpleScraper("youku")
    scraper.client = await scraper._create_client()
    
    try:
        # 优酷需要特殊的cookie和签名，这里只测试基本连接
        test_url = "https://acs.youku.com/h5/mtop.com.youku.aplatform.weakget/1.0/?jsv=2.5.1&appKey=24679788"
        print(f"📡 测试优酷API连接: {test_url}")
        
        response = await scraper.client.get(test_url)
        if response.status_code == 200:
            print("✅ 优酷API连接正常")
            print("ℹ️  优酷弹幕需要复杂的签名验证机制")
        else:
            print(f"❌ 优酷API连接失败 (状态码: {response.status_code})")
            
    except Exception as e:
        print(f"❌ 优酷测试失败: {e}")
    finally:
        await scraper.close()

# 芒果TV测试
async def test_mgtv_danmaku():
    print("\n🥭 测试芒果TV弹幕获取...")
    
    scraper = SimpleScraper("mgtv")
    scraper.client = await scraper._create_client()
    
    test_vid = "12345678"  # 示例视频ID
    
    try:
        # 测试芒果TV弹幕API
        url = f"https://bullet-ws.hitv.com/bullet/role/all/{test_vid}"
        print(f"📡 请求芒果TV弹幕: {url}")
        
        response = await scraper.client.get(url)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 芒果TV API响应正常")
            print(f"ℹ️  响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
        else:
            print(f"❌ 芒果TV API请求失败 (状态码: {response.status_code})")
            
    except Exception as e:
        print(f"❌ 芒果TV测试失败: {e}")
    finally:
        await scraper.close()

# 主测试函数
async def main():
    print("🚀 开始测试各平台弹幕获取功能...\n")
    print("=" * 60)
    
    # 依次测试各个平台
    await test_tencent_danmaku()
    await test_bilibili_danmaku()
    await test_iqiyi_danmaku()
    await test_youku_danmaku()
    await test_mgtv_danmaku()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")
    print("\n💡 提示:")
    print("- 如果要测试真实视频，请替换脚本中的视频ID")
    print("- 某些平台可能需要特殊的认证或cookie")
    print("- 完整功能需要运行完整的项目环境")

if __name__ == "__main__":
    asyncio.run(main())
